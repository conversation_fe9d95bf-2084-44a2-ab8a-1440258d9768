/**
 * Provider component cho Agent Configuration Accordion
 * Chỉ cho phép 1 config component mở tại một thời điểm
 */

import React, { useState, ReactNode } from 'react';
import {
  AgentConfigAccordionContext,
  AgentConfigAccordionContextType,
  ConfigComponentId
} from './AgentConfigAccordionContext.ts';

interface AgentConfigAccordionProviderProps {
  children: ReactNode;
  defaultOpenComponent?: ConfigComponentId | null;
}

const AgentConfigAccordionProvider: React.FC<AgentConfigAccordionProviderProps> = ({
  children,
  defaultOpenComponent = null,
}) => {
  const [openComponent, setOpenComponent] = useState<ConfigComponentId | null>(defaultOpenComponent);

  const toggleComponent = (componentId: ConfigComponentId) => {
    setOpenComponent(current => current === componentId ? null : componentId);
  };

  const isComponentOpen = (componentId: ConfigComponentId) => {
    return openComponent === componentId;
  };

  const value: AgentConfigAccordionContextType = {
    openComponent,
    setOpenComponent,
    toggleComponent,
    isComponentOpen,
  };

  return (
    <AgentConfigAccordionContext.Provider value={value}>
      {children}
    </AgentConfigAccordionContext.Provider>
  );
};

// Export default để tránh fast refresh warning
export default AgentConfigAccordionProvider;
