import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Button, EmptyState, Loading, Pagination } from '@/shared/components/common';
import React, { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { AgentGrid } from '../components';
// import { useGetAgents } from '../hooks/useAgent'; // Tạm thời comment để dùng mock data
import { useGetTypeAgents } from '../hooks/useTypeAgent';
// import { SortDirection } from '../types'; // DEMO: Không sử dụng cho mock data
// Import mock data để demo
import { mockAgents } from '../data/mockAgents';

/**
 * Trang hiển thị danh sách AI Agents
 */
const AIAgentsPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // Filter states
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(12);
  const [search, setSearch] = useState('');
  // const [sortBy] = useState('createdAt'); // DEMO: Không sử dụng cho mock data
  // const [sortDirection] = useState<SortDirection>(SortDirection.DESC); // DEMO: Không sử dụng cho mock data
  const [typeId, setTypeId] = useState<number | undefined>(undefined);
  // const [active] = useState<boolean | undefined>(undefined); // DEMO: Không sử dụng cho mock data

  // DEMO: Comment query params vì đang dùng mock data
  // const queryParams = useMemo<GetAgentsQueryDto>(() => ({
  //   page,
  //   limit,
  //   search: search || undefined,
  //   sortBy,
  //   sortDirection,
  //   typeId,
  //   active,
  // }), [page, limit, search, sortBy, sortDirection, typeId, active]);

  // Lấy danh sách type agents cho filter
  const { data: typeAgentsResponse } = useGetTypeAgents({
    page: 1,
    limit: 100, // Lấy tất cả để làm filter
  });

  // DEMO: Sử dụng mock data thay vì API call
  const isLoading = false;
  const error = null;
  const refetch = () => console.log('Refetch mock data');

  const handleSearch = (term: string) => {
    setSearch(term);
    setPage(1); // Reset về trang đầu khi search
  };

  const handleTypeFilter = useCallback((selectedTypeId: number | undefined) => {
    setTypeId(selectedTypeId);
    setPage(1); // Reset về trang đầu khi filter
  }, []);

  // const handleActiveFilter = (selectedActive: boolean | undefined) => {
  //   setActive(selectedActive);
  //   setPage(1); // Reset về trang đầu khi filter
  // };

  const handleAddAgent = () => {
    navigate('/ai-agents/add');
  };

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset về trang đầu khi thay đổi limit
  };

  // DEMO: Sử dụng mock data và filter client-side
  const filteredAgents = useMemo(() => {
    let filtered = [...mockAgents];

    // Filter theo search
    if (search) {
      filtered = filtered.filter(agent =>
        agent.name.toLowerCase().includes(search.toLowerCase()) ||
        agent.typeName.toLowerCase().includes(search.toLowerCase()) ||
        agent.model_id.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Filter theo typeId
    if (typeId) {
      filtered = filtered.filter(agent => agent.typeId === typeId);
    }

    return filtered;
  }, [search, typeId]);

  // Pagination cho mock data
  // const totalItems = filteredAgents.length; // DEMO: Không sử dụng biến này
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const agents = filteredAgents.slice(startIndex, endIndex);

  // Tạo menu items cho filter
  const filterMenuItems = useMemo(() => {
    const items = [
      {
        id: 'all',
        label: t('common.all', 'Tất cả'),
        icon: 'list',
        onClick: () => handleTypeFilter(undefined),
        isActive: typeId === undefined,
      },
    ];

    // Thêm các type agents vào menu
    const typeAgents = typeAgentsResponse?.result?.items || [];
    typeAgents.forEach(typeAgent => {
      items.push({
        id: `type-${typeAgent.id}`,
        label: typeAgent.name,
        icon: 'robot',
        onClick: () => handleTypeFilter(typeAgent.id),
        isActive: typeId === typeAgent.id,
      });
    });

    return items;
  }, [typeAgentsResponse?.result?.items, typeId, t, handleTypeFilter]);

  // Menu items cho active filter (sẽ sử dụng sau)
  // const activeFilterItems = [
  //   {
  //     id: 'active-all',
  //     label: t('common.all', 'Tất cả'),
  //     onClick: () => handleActiveFilter(undefined),
  //     isActive: active === undefined,
  //   },
  //   {
  //     id: 'active-true',
  //     label: t('common.active', 'Đang hoạt động'),
  //     onClick: () => handleActiveFilter(true),
  //     isActive: active === true,
  //   },
  //   {
  //     id: 'active-false',
  //     label: t('common.inactive', 'Không hoạt động'),
  //     onClick: () => handleActiveFilter(false),
  //     isActive: active === false,
  //   },
  // ];

  // Hiển thị loading
  if (isLoading) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleAddAgent}
          items={filterMenuItems}
        />
        <div className="flex justify-center items-center h-64">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  // Hiển thị lỗi
  if (error) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleAddAgent}
          items={filterMenuItems}
        />
        <EmptyState
          icon="alert-circle"
          title={t('common.error', 'Lỗi')}
          description={t('aiAgents.list.loadError', 'Không thể tải danh sách AI Agents. Vui lòng thử lại.')}
          actions={
            <Button
              variant="primary"
              onClick={() => refetch()}
            >
              {t('common.retry', 'Thử lại')}
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleAddAgent}
        items={filterMenuItems}
      />

      {agents.length > 0 ? (
        <>
          <AgentGrid agents={agents} />

          {/* Pagination - hiển thị cho mock data */}
          {filteredAgents.length > limit && (
            <div className="mt-6 flex justify-end">
              <Pagination
                currentPage={page}
                totalItems={filteredAgents.length}
                itemsPerPage={limit}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleLimitChange}
                itemsPerPageOptions={[6, 12, 24, 48]}
                showItemsPerPageSelector={true}
                showPageInfo={true}
                variant="compact"
                borderless={true}
              />
            </div>
          )}
        </>
      ) : (
        <EmptyState
          icon="robot"
          title={t('aiAgents.list.noAgents', 'Không có AI Agents')}
          description={
            search
              ? t('aiAgents.list.noSearchResults', 'Không tìm thấy AI Agents phù hợp với từ khóa tìm kiếm.')
              : t('aiAgents.list.noAgentsDescription', 'Hiện tại chưa có AI Agents nào. Hãy tạo Agent đầu tiên của bạn.')
          }
          actions={
            <Button
              variant="primary"
              onClick={handleAddAgent}
            >
              {t('aiAgents.list.createFirst', 'Tạo AI Agent đầu tiên')}
            </Button>
          }
        />
      )}
    </div>
  );
};

export default AIAgentsPage;
