import { AgentDetailDto, TypeAgentDetailDto } from '../types';

/**
 * Mock data cho agent detail để test trang edit
 */
export const mockAgentDetail: AgentDetailDto = {
  id: 'agent-001',
  name: 'AI Assistant Pro',
  avatar: '/assets/images/avatars/agent-001.png',
  active: true,
  level: 5,
  exp: 750,
  expMax: 1000,
  model_id: 'gpt-4o',
  typeName: 'Business Assistant',
  typeId: 1,
  createdAt: '2024-01-15T10:30:00Z',
  updatedAt: '2024-01-20T14:45:00Z',
  
  // Model Configuration
  modelConfig: {
    modelId: 'gpt-4o',
    temperature: 0.7,
    top_p: 0.9,
    top_k: 40,
    max_tokens: 2000,
    frequency_penalty: 0.0,
    presence_penalty: 0.0,
  },

  // Profile Configuration
  profile: {
    gender: 'FEMALE',
    age: 28,
    personality: 'Friendly, professional, and helpful. Always maintains a positive attitude while being efficient and detail-oriented.',
    background: 'Experienced business consultant with expertise in project management, data analysis, and customer service.',
    expertise: ['Business Analysis', 'Project Management', 'Customer Service', 'Data Analytics'],
    communication_style: 'Clear, concise, and professional communication with a warm and approachable tone.',
    goals: 'To help users achieve their business objectives efficiently while providing excellent customer experience.',
  },

  // Integration Configuration
  integrations: {
    vectorStoreId: 'vs-001',
    knowledgeBaseIds: ['kb-001', 'kb-002'],
    toolIds: ['tool-calculator', 'tool-calendar', 'tool-email'],
    apiKeys: {
      'google-calendar': 'encrypted-key-001',
      'email-service': 'encrypted-key-002',
    },
    webhooks: [
      {
        id: 'webhook-001',
        url: 'https://api.example.com/webhook/agent-001',
        events: ['message.sent', 'task.completed'],
        active: true,
      },
    ],
  },

  // Strategy Configuration
  strategy: {
    approach: 'CONSULTATIVE',
    priority: 'EFFICIENCY',
    decision_making: 'DATA_DRIVEN',
    communication_frequency: 'REGULAR',
    escalation_rules: [
      {
        condition: 'user_frustration_detected',
        action: 'escalate_to_human',
        threshold: 3,
      },
      {
        condition: 'complex_technical_query',
        action: 'use_specialized_tools',
        threshold: 1,
      },
    ],
    fallback_responses: [
      "I understand this might be complex. Let me break it down step by step.",
      "I'm here to help. Could you provide more details about what you're looking for?",
      "Let me connect you with additional resources that might be helpful.",
    ],
  },

  // Response Configuration
  response: {
    tone: 'PROFESSIONAL_FRIENDLY',
    length: 'MEDIUM',
    format: 'STRUCTURED',
    include_examples: true,
    include_sources: true,
    language_style: 'BUSINESS_CASUAL',
    emoji_usage: 'MINIMAL',
    personalization_level: 'HIGH',
    follow_up_questions: true,
  },

  // Convert Configuration
  convert: {
    input_formats: ['TEXT', 'PDF', 'DOCX', 'CSV'],
    output_formats: ['TEXT', 'JSON', 'MARKDOWN', 'HTML'],
    transformation_rules: [
      {
        from: 'PDF',
        to: 'TEXT',
        preserve_formatting: true,
        extract_tables: true,
      },
      {
        from: 'CSV',
        to: 'JSON',
        include_headers: true,
        data_types: 'auto_detect',
      },
    ],
    quality_settings: {
      accuracy: 'HIGH',
      speed: 'MEDIUM',
      preserve_structure: true,
    },
  },

  // Multi-Agent Configuration
  multiAgent: {
    isEnabled: true,
    coordinatorRole: 'PRIMARY',
    subAgentIds: ['agent-002', 'agent-003'],
    collaboration_rules: [
      {
        trigger: 'technical_question',
        delegate_to: 'agent-002',
        return_control: true,
      },
      {
        trigger: 'creative_task',
        delegate_to: 'agent-003',
        return_control: false,
      },
    ],
    communication_protocol: 'SEQUENTIAL',
    conflict_resolution: 'COORDINATOR_DECIDES',
  },

  // Statistics
  statistics: {
    total_conversations: 1250,
    total_messages: 8750,
    average_response_time: 2.3,
    satisfaction_rating: 4.7,
    success_rate: 92.5,
    last_active: '2024-01-20T14:45:00Z',
  },

  // Permissions
  permissions: {
    can_edit: true,
    can_delete: true,
    can_share: true,
    can_export: true,
    can_duplicate: true,
  },
};

/**
 * Mock data cho type agent detail
 */
export const mockTypeAgentDetail: TypeAgentDetailDto = {
  id: 1,
  name: 'Business Assistant',
  description: 'Comprehensive business assistant for professional tasks',
  icon: 'briefcase',
  category: 'Business',
  isActive: true,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-15T10:00:00Z',
  
  config: {
    hasProfile: true,
    hasResources: true,
    hasStrategy: true,
    hasOutput: true,
    hasConversion: true,
    hasMultiAgent: true,
    
    // Default configurations
    defaultModelConfig: {
      temperature: 0.7,
      top_p: 0.9,
      top_k: 40,
      max_tokens: 2000,
    },
    
    availableTools: [
      'calculator',
      'calendar',
      'email',
      'document-processor',
      'data-analyzer',
    ],
    
    supportedFormats: {
      input: ['TEXT', 'PDF', 'DOCX', 'CSV', 'XLSX'],
      output: ['TEXT', 'JSON', 'MARKDOWN', 'HTML', 'PDF'],
    },
    
    capabilities: [
      'natural_language_processing',
      'data_analysis',
      'document_generation',
      'task_automation',
      'multi_language_support',
    ],
  },
  
  // Usage statistics
  usage: {
    total_agents: 156,
    active_agents: 142,
    total_conversations: 25680,
    average_satisfaction: 4.6,
  },
};

/**
 * Function để lấy mock agent detail theo ID
 */
export const getMockAgentDetail = (id: string): AgentDetailDto => {
  // Trong thực tế, có thể có nhiều mock agents khác nhau
  // Hiện tại chỉ return cùng một mock data
  return {
    ...mockAgentDetail,
    id,
    name: `AI Assistant ${id.slice(-3)}`,
  };
};

/**
 * Function để lấy mock type agent detail theo ID
 */
export const getMockTypeAgentDetail = (id: number): TypeAgentDetailDto => {
  return {
    ...mockTypeAgentDetail,
    id,
  };
};
