import React, { useState } from 'react';
import { Card, IconCard, ProgressBar, Tooltip, Chip, Modal, Button, Typography } from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm/SlideInForm';
import { useTranslation } from 'react-i18next';
import { AgentListItemDto } from '../types';
import { useDeleteAgent, useToggleAgentActive } from '../hooks/useAgent';
import { useGetAgentDetailWithService, useGetTypeAgentDetailWithService } from '../hooks/useAgentService';
import { NotificationUtil } from '@/shared/utils/notification';
import { AgentConfigurationForm } from './agent-config/AgentConfigurationForm';
import { EditAgentForm } from './agent-config/EditAgentForm';

// Mock data đã được move sang file riêng: ../data/mockAgents.ts

interface AgentCardProps {
  agent: AgentListItemDto;
}

/**
 * Component hiển thị thông tin của một AI Agent
 */
const AgentCard: React.FC<AgentCardProps> = ({ agent }) => {
  const { t } = useTranslation();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [showEditAgentForm, setShowEditAgentForm] = useState(false);
  const [isActive, setIsActive] = useState(agent.active);

  // Hooks cho API calls
  const deleteAgentMutation = useDeleteAgent();
  const toggleAgentActiveMutation = useToggleAgentActive();

  // Hooks để lấy chi tiết agent khi edit
  const {
    data: agentDetailResponse,
    isLoading: isLoadingAgentDetail,
    // refetch: refetchAgentDetail // Không sử dụng trong EditAgentForm mới
  } = useGetAgentDetailWithService(agent.id);

  // Hooks để lấy chi tiết type agent
  const {
    data: typeAgentDetailResponse,
    isLoading: isLoadingTypeAgentDetail,
    // refetch: refetchTypeAgentDetail // Không sử dụng trong EditAgentForm mới
  } = useGetTypeAgentDetailWithService(agent.typeId);

  const handleEditAgent = () => {
    // Hiển thị form edit agent mới
    setShowEditAgentForm(true);
  };

  const handleToggleActive = async () => {
    try {
      await toggleAgentActiveMutation.mutateAsync(agent.id);
      setIsActive(!isActive);

      NotificationUtil.success({
        message: isActive
          ? t('aiAgents.deactivateSuccess', 'Đã tắt agent thành công')
          : t('aiAgents.activateSuccess', 'Đã bật agent thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error toggling agent active:', error);
      NotificationUtil.error({
        message: t('aiAgents.toggleError', 'Có lỗi xảy ra khi thay đổi trạng thái agent'),
        duration: 5000,
      });
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteAgentMutation.mutateAsync(agent.id);
      setShowDeleteModal(false);

      NotificationUtil.success({
        message: t('aiAgents.deleteSuccess', 'Đã xóa agent thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting agent:', error);
      NotificationUtil.error({
        message: t('aiAgents.deleteError', 'Có lỗi xảy ra khi xóa agent'),
        duration: 5000,
      });
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  // Handlers cho edit form
  const handleEditFormSuccess = () => {
    setShowEditForm(false);

    NotificationUtil.success({
      message: t('aiAgents.updateSuccess', 'Cập nhật agent thành công!'),
      duration: 3000,
    });

    // Có thể trigger refresh danh sách ở đây nếu cần
    // onRefresh?.();
  };

  const handleEditFormCancel = () => {
    setShowEditForm(false);
  };

  // Tính toán phần trăm kinh nghiệm
  const experiencePercent = Math.round((agent.exp / agent.expMax) * 100);

  // Xác định variant cho model chip dựa trên nhà cung cấp
  const getModelVariant = (
    model: string
  ): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {
    const modelLower = model.toLowerCase();

    // OpenAI models
    if (modelLower.includes('gpt')) return 'danger';

    // Anthropic models
    if (modelLower.includes('claude')) return 'success';

    // Google models
    if (modelLower.includes('gemini')) return 'info';

    // DeepSeek models
    if (modelLower.includes('deepseek')) return 'warning';

    // Mistral models
    if (modelLower.includes('mistral')) return 'primary';

    // Llama models
    if (modelLower.includes('llama')) return 'info';

    // Default for other models
    return 'primary';
  };

  // Không cần xác định layout vì đã cố định

  return (
    <>
      <Card
        className="h-full overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300"
        variant="elevated"
      >
      <div className="p-4">
        <div className="flex flex-col space-y-4">
          {/* Hàng 1: Avatar, tên, loại agent và model */}
          <div className="flex items-center gap-3 overflow-hidden">
            {/* Avatar và khung level */}
            <div className="relative w-16 h-16 sm:w-20 sm:h-20 flex-shrink-0">
              <div className="w-full h-full relative">
                <img
                  src="/assets/images/frame-level-agents.png"
                  alt="Level frame"
                  className="absolute inset-0 w-full h-full object-contain z-10"
                />
                <div className="absolute inset-0 flex items-center justify-center z-0">
                  <img
                    src={agent.avatar || '/assets/images/default-avatar.png'}
                    alt={agent.name}
                    className="w-[75%] h-[75%] rounded-full"
                  />
                </div>
                {/* Chỉ báo trạng thái active */}
                <div
                  className={`absolute bottom-1 right-1 w-3 h-3 rounded-full z-20 ${
                    isActive ? 'bg-green-500 dark:bg-green-400' : 'bg-gray-300 dark:bg-gray-600'
                  }`}
                />
              </div>
            </div>

            {/* Thông tin agent: tên, loại và model */}
            <div className="flex flex-col min-w-0 flex-grow">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2">
                <div className="min-w-0">
                  <Typography variant="h5" className="font-semibold text-gray-900 dark:text-white truncate">
                    {agent.name}
                  </Typography>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {agent.typeName}
                  </div>
                </div>
                <div className="flex-shrink-0 mt-1 sm:mt-0">
                  <Chip
                    variant={getModelVariant(agent.model_id)}
                    size="sm"
                    className="font-normal max-w-full truncate"
                  >
                    {agent.model_id}
                  </Chip>
                </div>
              </div>
            </div>
          </div>

          {/* Hàng 2: Level/exp và các nút chức năng */}
          <div className="flex flex-col">
            {/* Thông tin level và exp */}
            <div>
              <div className="flex justify-between items-center mb-1">
                <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                  Cấp độ: {agent.level}
                </span>
                <Chip variant="danger" size="sm" className="font-medium text-xs">
                  {experiencePercent}%
                </Chip>
              </div>
              <ProgressBar
                value={experiencePercent}
                size="sm"
                color="primary"
                gradient
                rounded
                className="mb-3"
              />
            </div>

            {/* Các nút chức năng */}
            <div className="flex justify-end space-x-6">
              <Tooltip
                content={isActive ? t('common.deactivate') : t('common.activate')}
                position="top"
              >
                <IconCard
                  icon="power"
                  variant={isActive ? 'primary' : 'default'}
                  size="md"
                  onClick={handleToggleActive}
                  className={isActive ? 'text-green-500' : 'text-gray-400'}
                  disabled={toggleAgentActiveMutation.isPending}
                />
              </Tooltip>
              <Tooltip content={t('common.edit')} position="top">
                <IconCard icon="edit" variant="default" size="md" onClick={handleEditAgent} />
              </Tooltip>
              <Tooltip content={t('common.delete')} position="top">
                <IconCard
                  icon="trash"
                  variant="default"
                  size="md"
                  onClick={handleDeleteClick}
                  className="text-red-500 hover:text-red-600"
                  disabled={deleteAgentMutation.isPending}
                />
              </Tooltip>
            </div>
          </div>
        </div>
      </div>
    </Card>

    {/* Modal xác nhận xóa */}
    <Modal
      isOpen={showDeleteModal}
      onClose={handleDeleteCancel}
      title={t('aiAgents.deleteConfirmTitle', 'Xác nhận xóa Agent')}
      size="md"
      footer={
        <div className="flex justify-end gap-3">
          <Button
            variant="outline"
            onClick={handleDeleteCancel}
            disabled={deleteAgentMutation.isPending}
          >
            {t('common.cancel', 'Hủy')}
          </Button>
          <Button
            variant="danger"
            onClick={handleDeleteConfirm}
            isLoading={deleteAgentMutation.isPending}
          >
            {t('common.delete', 'Xóa')}
          </Button>
        </div>
      }
    >
      <div className="space-y-4">
        <p className="text-gray-600 dark:text-gray-300">
          {t('aiAgents.deleteConfirmMessage', 'Bạn có chắc chắn muốn xóa Agent này không? Hành động này không thể hoàn tác.')}
        </p>

        <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
          <div className="flex items-center gap-3">
            <img
              src={agent.avatar || '/assets/images/default-avatar.png'}
              alt={agent.name}
              className="w-10 h-10 rounded-full"
            />
            <div>
              <p className="font-medium text-gray-900 dark:text-white">{agent.name}</p>
              <p className="text-sm text-gray-500 dark:text-gray-400">{agent.typeName}</p>
            </div>
          </div>
        </div>
      </div>
    </Modal>

    {/* SlideInForm cho edit agent */}
    <SlideInForm isVisible={showEditForm}>
      {agentDetailResponse?.result && typeAgentDetailResponse?.result && (
        <AgentConfigurationForm
          mode="edit"
          agentId={agent.id}
          agentDetailData={agentDetailResponse.result}
          typeAgentConfig={typeAgentDetailResponse.result.config}
          onSuccess={handleEditFormSuccess}
          onCancel={handleEditFormCancel}
        />
      )}

      {/* Loading state */}
      {(isLoadingAgentDetail || isLoadingTypeAgentDetail) && (
        <div className="flex justify-center items-center py-12">
          <div className="text-gray-500">Đang tải dữ liệu...</div>
        </div>
      )}
    </SlideInForm>

    {/* EditAgentForm mới */}
    <EditAgentForm
      agent={agent}
      isOpen={showEditAgentForm}
      onClose={() => setShowEditAgentForm(false)}
      onSuccess={() => {
        setShowEditAgentForm(false);
        // Có thể trigger refresh danh sách ở đây nếu cần
        // onRefresh?.();
      }}
    />
    </>
  );
};

export default AgentCard;

// Mock data đã được move sang file riêng: ../data/mockAgents.ts
// AgentCardDemo component đã được move sang file riêng để tránh fast refresh warning
