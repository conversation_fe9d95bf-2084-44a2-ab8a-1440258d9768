import { AgentListItemDto } from '../types';

/**
 * Mock data cho demo - 5 AI agents với các model khác nhau
 */
export const mockAgents: AgentListItemDto[] = [
  {
    id: 'agent-001',
    name: 'ChatGPT Assistant',
    avatar: 'https://cdn.redai.vn/avatars/chatgpt-avatar.png',
    typeId: 1,
    typeName: 'Trợ lý thông minh',
    exp: 8500,
    expMax: 10000,
    level: 15,
    badge_url: 'https://cdn.redai.vn/badges/expert-badge.png',
    model_id: 'gpt-4o',
    active: true,
    createdAt: Date.now() - 86400000 * 30, // 30 ngày trước
    updatedAt: Date.now() - 86400000 * 2   // 2 ngày trước
  },
  {
    id: 'agent-002',
    name: '<PERSON>',
    avatar: 'https://cdn.redai.vn/avatars/claude-avatar.png',
    typeId: 2,
    typeName: '<PERSON><PERSON> tích dữ liệu',
    exp: 6200,
    expMax: 8000,
    level: 12,
    badge_url: 'https://cdn.redai.vn/badges/analyst-badge.png',
    model_id: 'claude-3.5-sonnet',
    active: true,
    createdAt: Date.now() - 86400000 * 45, // 45 ngày trước
    updatedAt: Date.now() - 86400000 * 1   // 1 ngày trước
  },
  {
    id: 'agent-003',
    name: 'Gemini Creative',
    avatar: 'https://cdn.redai.vn/avatars/gemini-avatar.png',
    typeId: 3,
    typeName: 'Sáng tạo nội dung',
    exp: 3400,
    expMax: 5000,
    level: 8,
    badge_url: 'https://cdn.redai.vn/badges/creative-badge.png',
    model_id: 'gemini-1.5-pro',
    active: false,
    createdAt: Date.now() - 86400000 * 20, // 20 ngày trước
    updatedAt: Date.now() - 86400000 * 5   // 5 ngày trước
  },
  {
    id: 'agent-004',
    name: 'DeepSeek Coder',
    avatar: 'https://cdn.redai.vn/avatars/deepseek-avatar.png',
    typeId: 4,
    typeName: 'Lập trình viên',
    exp: 9800,
    expMax: 10000,
    level: 20,
    badge_url: 'https://cdn.redai.vn/badges/master-badge.png',
    model_id: 'deepseek-v3',
    active: true,
    createdAt: Date.now() - 86400000 * 60, // 60 ngày trước
    updatedAt: Date.now() - 86400000 * 3   // 3 ngày trước
  },
  {
    id: 'agent-005',
    name: 'Llama Helper',
    avatar: null, // Sử dụng avatar mặc định
    typeId: 5,
    typeName: 'Hỗ trợ khách hàng',
    exp: 1200,
    expMax: 3000,
    level: 3,
    badge_url: 'https://cdn.redai.vn/badges/beginner-badge.png',
    model_id: 'llama-3.1-70b',
    active: false,
    createdAt: Date.now() - 86400000 * 10, // 10 ngày trước
    updatedAt: Date.now() - 86400000 * 7   // 7 ngày trước
  }
];
