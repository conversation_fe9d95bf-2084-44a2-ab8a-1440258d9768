import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  CollapsibleCard,
  Button,
  Icon,
  Card,
  FormItem,
  Input,
  Typography,
  EmptyState,
  Toggle,
} from '@/shared/components/common';
import { Module } from '../../types/agent.types';
import { useUpdateAgent } from '../../hooks';

/**
 * Props cho component ModuleSection
 */
interface ModuleSectionProps {
  /**
   * ID của Agent
   */
  agentId: string;

  /**
   * Danh sách module của Agent
   */
  modules: Module[];

  /**
   * Callback khi trạng thái đóng/mở thay đổi
   */
  onToggle?: (isOpen: boolean) => void;
}

/**
 * Component hiển thị các module của Agent
 */
const ModuleSection: React.FC<ModuleSectionProps> = ({ agentId, modules, onToggle }) => {
  const { t } = useTranslation();
  const [localModules, setLocalModules] = useState<Module[]>(modules);
  const [originalModules, setOriginalModules] = useState<Module[]>(modules);
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // Sử dụng hooks
  const { mutate: updateAgent, isPending: isUpdating } = useUpdateAgent();

  // Cập nhật state khi modules thay đổi từ props
  useEffect(() => {
    setLocalModules(modules);
    setOriginalModules(modules);
    setHasChanges(false);
  }, [modules]);

  /**
   * Kiểm tra xem có thay đổi so với dữ liệu ban đầu không
   */
  const checkForChanges = (currentModules: Module[] = localModules) => {
    // So sánh từng module
    const hasModuleChanges = currentModules.some((currentModule, index) => {
      const originalModule = originalModules[index];

      // Kiểm tra trạng thái bật/tắt
      if (currentModule.isEnabled !== originalModule.isEnabled) {
        return true;
      }

      // Kiểm tra cấu hình
      const currentConfig = currentModule.configuration;
      const originalConfig = originalModule.configuration;

      // Lấy tất cả các key từ cả hai cấu hình
      const allKeys = new Set([
        ...Object.keys(currentConfig),
        ...Object.keys(originalConfig)
      ]);

      // Kiểm tra từng key
      return Array.from(allKeys).some(key => {
        return currentConfig[key] !== originalConfig[key];
      });
    });

    setHasChanges(hasModuleChanges);
  };

  /**
   * Xử lý bật/tắt module
   */
  const handleToggleModule = (moduleId: string) => {
    setLocalModules(prev => {
      const updatedModules = prev.map(module =>
        module.id === moduleId ? { ...module, isEnabled: !module.isEnabled } : module
      );

      // Kiểm tra thay đổi sau khi cập nhật
      setTimeout(() => checkForChanges(updatedModules), 0);

      return updatedModules;
    });
  };

  /**
   * Xử lý thay đổi cấu hình module
   */
  const handleConfigChange = (moduleId: string, key: string, value: number | string | boolean) => {
    setLocalModules(prev => {
      const updatedModules = prev.map(module =>
        module.id === moduleId
          ? {
              ...module,
              configuration: {
                ...module.configuration,
                [key]: value,
              },
            }
          : module
      );

      // Kiểm tra thay đổi sau khi cập nhật
      setTimeout(() => checkForChanges(updatedModules), 0);

      return updatedModules;
    });
  };

  /**
   * Xử lý lưu thay đổi
   */
  const handleSave = () => {
    if (!hasChanges) return;

    updateAgent(
      {
        id: agentId,
        data: {
          modules: localModules,
        },
      },
      {
        onSuccess: () => {
          // Cập nhật original modules
          setOriginalModules([...localModules]);
          setHasChanges(false);

          // Hiển thị thông báo thành công
          alert(t('aiAgents.module.saveSuccess', 'Lưu cấu hình module thành công!'));
        },
        onError: (error) => {
          // Hiển thị thông báo lỗi
          console.error('Update modules error:', error);
          alert(t('aiAgents.module.saveError', 'Có lỗi xảy ra khi lưu cấu hình module. Vui lòng thử lại.'));
        }
      }
    );
  };

  // Xử lý khi đóng/mở card
  const handleCardToggle = (isOpen: boolean) => {
    if (onToggle) {
      onToggle(isOpen);
    }
  };

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center">
          <Icon name="puzzle" className="mr-2 text-orange-500" />
          <span>{t('aiAgents.module.title', 'Module')}</span>
        </div>
      }
      className="mb-6"
      onToggle={handleCardToggle}
    >
      <div className="mb-4">
        <Typography variant="body1">
          {t(
            'aiAgents.module.description',
            'Cấu hình các module và thông số LLM cho Agent.'
          )}
        </Typography>
      </div>

      {/* Danh sách module */}
      {localModules.length === 0 ? (
        <EmptyState
          icon="puzzle"
          title={t('aiAgents.module.noModules', 'Chưa có module nào')}
          description={t(
            'aiAgents.module.noModulesDescription',
            'Hiện tại không có module nào khả dụng.'
          )}
        />
      ) : (
        <div className="space-y-6 mb-6">
          {localModules.map(module => (
            <Card key={module.id} className="p-6">
              <div className="flex justify-between items-center mb-4">
                <div>
                  <Typography variant="h6">{module.name}</Typography>
                  {module.description && (
                    <Typography variant="body2" className="text-gray-500">
                      {module.description}
                    </Typography>
                  )}
                </div>
                <Toggle
                  checked={module.isEnabled}
                  onChange={() => handleToggleModule(module.id)}
                  label={
                    module.isEnabled
                      ? t('common.enabled', 'Đã bật')
                      : t('common.disabled', 'Đã tắt')
                  }
                />
              </div>

              {module.isEnabled && (
                <div className="space-y-4">
                  <Typography variant="subtitle1">
                    {t('aiAgents.module.configuration', 'Cấu hình')}
                  </Typography>

                  {/* Max Tokens */}
                  {module.configuration.maxTokens !== undefined && (
                    <FormItem
                      label={t('aiAgents.module.maxTokens', 'Max Tokens')}
                      helpText={t(
                        'aiAgents.module.maxTokensHelp',
                        'Số lượng token tối đa cho mỗi lần gọi API'
                      )}
                    >
                      <div className="flex items-center">
                        <div className="flex-1 mr-4">
                          
                          <input 
                            type="range"
                            value={module.configuration.maxTokens}
                            onChange={(e) =>
                              handleConfigChange(module.id, 'maxTokens', Number(e.target.value))
                            }
                            min={1}
                            max={4096}
                            step={1}
                            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                          />
                        </div>
                        <Input
                          type="number"
                          value={module.configuration.maxTokens}
                          onChange={e =>
                            handleConfigChange(
                              module.id,
                              'maxTokens',
                              Number(e.target.value)
                            )
                          }
                          min={1}
                          max={4096}
                          className="w-24"
                        />
                      </div>
                    </FormItem>
                  )}

                  {/* Temperature */}
                  {module.configuration.temperature !== undefined && (
                    <FormItem
                      label={t('aiAgents.module.temperature', 'Temperature')}
                      helpText={t(
                        'aiAgents.module.temperatureHelp',
                        'Mức độ ngẫu nhiên trong kết quả (0-1)'
                      )}
                    >
                      <div className="flex items-center">
                        <div className="flex-1 mr-4">
                          <input 
                            type="range"
                            value={module.configuration.temperature}
                            onChange={(e) =>
                              handleConfigChange(module.id, 'temperature', Number(e.target.value))
                            }
                            min={0}
                            max={1}
                            step={0.01}
                            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                          />
                        </div>
                        <Input
                          type="number"
                          value={module.configuration.temperature}
                          onChange={e =>
                            handleConfigChange(
                              module.id,
                              'temperature',
                              Number(e.target.value)
                            )
                          }
                          min={0}
                          max={1}
                          step={0.01}
                          className="w-24"
                        />
                      </div>
                    </FormItem>
                  )}

                  {/* Top P */}
                  {module.configuration.topP !== undefined && (
                    <FormItem
                      label={t('aiAgents.module.topP', 'Top P')}
                      helpText={t(
                        'aiAgents.module.topPHelp',
                        'Xác suất tích lũy cho lựa chọn token (0-1)'
                      )}
                    >
                      <div className="flex items-center">
                        <div className="flex-1 mr-4">
                          <input 
                            type="range"
                            value={module.configuration.topP}
                            onChange={(e) =>
                              handleConfigChange(module.id, 'topP', Number(e.target.value))
                            }
                            min={0}
                            max={1}
                            step={0.01}
                            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                          />
                        </div>
                        <Input
                          type="number"
                          value={module.configuration.topP}
                          onChange={e =>
                            handleConfigChange(
                              module.id,
                              'topP',
                              Number(e.target.value)
                            )
                          }
                          min={0}
                          max={1}
                          step={0.01}
                          className="w-24"
                        />
                      </div>
                    </FormItem>
                  )}

                  {/* Top K */}
                  {module.configuration.topK !== undefined && (
                    <FormItem
                      label={t('aiAgents.module.topK', 'Top K')}
                      helpText={t(
                        'aiAgents.module.topKHelp',
                        'Số lượng token có xác suất cao nhất để xem xét'
                      )}
                    >
                      <div className="flex items-center">
                        <div className="flex-1 mr-4">
                          <input 
                            type="range"
                            value={module.configuration.topK}
                            onChange={(e) =>
                              handleConfigChange(module.id, 'topK', Number(e.target.value))
                            }
                            min={1}
                            max={100}
                            step={1}
                            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                          />
                        </div>
                        <Input
                          type="number"
                          value={module.configuration.topK}
                          onChange={e =>
                            handleConfigChange(
                              module.id,
                              'topK',
                              Number(e.target.value)
                            )
                          }
                          min={1}
                          max={100}
                          className="w-24"
                        />
                      </div>
                    </FormItem>
                  )}
                </div>
              )}
            </Card>
          ))}
        </div>
      )}

      {localModules.length > 0 && (
        <div className="flex justify-end">
          <Button
            variant="primary"
            onClick={handleSave}
            isLoading={isUpdating}
            disabled={!hasChanges}
            leftIcon={<Icon name="save" size="sm" />}
          >
            {t('common.save', 'Lưu')}
          </Button>
        </div>
      )}
    </CollapsibleCard>
  );
};

export default ModuleSection;
