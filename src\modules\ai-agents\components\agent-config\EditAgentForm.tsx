import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Button,
  Typography,
  Input,
  Textarea,
  Avatar,
  Grid,
  Card,
  Chip,
  ProgressBar,
  Modal,
  Icon,
  Loading
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm/SlideInForm';
import { AgentListItemDto } from '../../types';
import { useUpdateAgentWithService, useDeleteAgentWithService } from '../../hooks/useAgentService';
import { NotificationUtil } from '@/shared/utils/notification';

interface EditAgentFormProps {
  agent: AgentListItemDto;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

interface FormData {
  name: string;
  description?: string;
  avatar?: string;
  active: boolean;
}

export const EditAgentForm: React.FC<EditAgentFormProps> = ({
  agent,
  isOpen,
  onClose,
  onSuccess
}) => {
  const { t } = useTranslation('aiAgents');
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Form state
  const [formData, setFormData] = useState<FormData>({
    name: agent.name,
    description: '',
    avatar: agent.avatar || undefined,
    active: agent.active
  });
  
  const [, setAvatarFile] = useState<File | null>(null); // TODO: Implement avatar upload
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // API hooks
  const updateAgentMutation = useUpdateAgentWithService();
  const deleteAgentMutation = useDeleteAgentWithService();

  // Reset form when agent changes
  useEffect(() => {
    setFormData({
      name: agent.name,
      description: '',
      avatar: agent.avatar || undefined,
      active: agent.active
    });
    setErrors({});
    setAvatarFile(null);
  }, [agent]);

  // Validation
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = t('editAgent.validation.nameRequired');
    } else if (formData.name.trim().length < 2) {
      newErrors.name = t('editAgent.validation.nameMinLength');
    } else if (formData.name.trim().length > 100) {
      newErrors.name = t('editAgent.validation.nameMaxLength');
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = t('editAgent.validation.descriptionMaxLength');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form input changes
  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Handle avatar upload
  const handleAvatarClick = () => {
    fileInputRef.current?.click();
  };

  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const imageUrl = URL.createObjectURL(file);
      setFormData(prev => ({ ...prev, avatar: imageUrl }));
      setAvatarFile(file);
    }
  };

  // Handle save
  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      const updateData = {
        name: formData.name.trim(),
        active: formData.active,
        // Add other fields as needed based on your API
      };

      await updateAgentMutation.mutateAsync({
        id: agent.id,
        data: updateData
      });

      NotificationUtil.success({
        message: t('editAgent.updateSuccess'),
        duration: 3000,
      });

      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Update agent error:', error);
      NotificationUtil.error({
        message: t('editAgent.updateError'),
        duration: 5000,
      });
    }
  };

  // Handle delete
  const handleDelete = async () => {
    try {
      await deleteAgentMutation.mutateAsync(agent.id);

      NotificationUtil.success({
        message: t('editAgent.deleteSuccess'),
        duration: 3000,
      });

      setShowDeleteModal(false);
      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Delete agent error:', error);
      NotificationUtil.error({
        message: t('editAgent.deleteError'),
        duration: 5000,
      });
    }
  };

  // Calculate progress percentage
  const progressPercentage = Math.round((agent.exp / agent.expMax) * 100);

  const isLoading = updateAgentMutation.isPending || deleteAgentMutation.isPending;

  // Debug log
  console.log('EditAgentForm render:', { isOpen, agent: agent.name });

  return (
    <>
      {/* Overlay backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-end"
          onClick={onClose}
        >
          <SlideInForm
            isVisible={isOpen}
            className="w-full max-w-4xl h-full"
          >
            <div
              className="bg-white dark:bg-gray-900 h-full p-6 space-y-6 overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
          {/* Header with title and close button */}
          <div className="flex justify-between items-center border-b pb-4">
            <div>
              <Typography variant="h4" className="text-foreground font-bold">
                {t('editAgent.title')}
              </Typography>
              <Typography variant="body2" className="text-muted-foreground mt-1">
                {t('editAgent.subtitle')}
              </Typography>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="flex items-center space-x-2"
            >
              <Icon name="x" size="sm" />
            </Button>
          </div>

          {isLoading && (
            <div className="flex justify-center py-4">
              <Loading size="md" />
            </div>
          )}

          <Grid container spacing={6}>
            {/* Left Column - Avatar and Status */}
            <Grid item xs={12} md={4}>
              <Card className="p-4 space-y-4">
                {/* Avatar Section */}
                <div className="text-center space-y-3">
                  <Typography variant="h6" className="text-foreground">
                    {t('editAgent.agentAvatar')}
                  </Typography>
                  
                  <div className="relative inline-block">
                    <Avatar
                      src={formData.avatar}
                      alt={formData.name}
                      size="xl"
                      className="cursor-pointer hover:opacity-80 transition-opacity"
                      onClick={handleAvatarClick}
                    />
                    <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity rounded-full cursor-pointer"
                         onClick={handleAvatarClick}>
                      <Icon name="camera" size="md" className="text-white" />
                    </div>
                  </div>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleAvatarClick}
                    className="w-full"
                  >
                    {t('editAgent.changeAvatar')}
                  </Button>
                  
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleAvatarChange}
                    className="hidden"
                  />
                </div>

                {/* Agent Stats */}
                <div className="space-y-3 pt-4 border-t">
                  <div className="flex justify-between items-center">
                    <Typography variant="body2" className="text-muted-foreground">
                      {t('editAgent.agentLevel')}
                    </Typography>
                    <Chip variant="secondary" size="sm">
                      {agent.level}
                    </Chip>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Typography variant="body2" className="text-muted-foreground">
                        {t('editAgent.agentExp')}
                      </Typography>
                      <Typography variant="body2" className="text-foreground">
                        {agent.exp}/{agent.expMax}
                      </Typography>
                    </div>
                    <ProgressBar
                      value={progressPercentage}
                      className="h-2"
                      variant="default"
                    />
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <Typography variant="body2" className="text-muted-foreground">
                      {t('editAgent.agentModel')}
                    </Typography>
                    <Chip variant="outline" size="sm">
                      {agent.model_id}
                    </Chip>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <Typography variant="body2" className="text-muted-foreground">
                      {t('editAgent.agentType')}
                    </Typography>
                    <Typography variant="body2" className="text-foreground">
                      {agent.typeName}
                    </Typography>
                  </div>
                </div>
              </Card>
            </Grid>

            {/* Right Column - Form Fields */}
            <Grid item xs={12} md={8}>
              <Card className="p-4 space-y-4">
                <Typography variant="h6" className="text-foreground">
                  {t('editAgent.basicInfo')}
                </Typography>

                {/* Agent Name */}
                <div className="space-y-2">
                  <Typography variant="body2" className="text-foreground font-medium">
                    {t('editAgent.agentName')} *
                  </Typography>
                  <Input
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder={t('editAgent.agentNamePlaceholder')}
                    error={!!errors.name}
                    helperText={errors.name}
                    disabled={isLoading}
                  />
                </div>

                {/* Agent Description */}
                <div className="space-y-2">
                  <Typography variant="body2" className="text-foreground font-medium">
                    {t('editAgent.agentDescription')}
                  </Typography>
                  <Textarea
                    value={formData.description || ''}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder={t('editAgent.agentDescriptionPlaceholder')}
                    rows={3}
                    error={!!errors.description}
                    helperText={errors.description}
                    disabled={isLoading}
                  />
                </div>

                {/* Agent Status */}
                <div className="space-y-2">
                  <Typography variant="body2" className="text-foreground font-medium">
                    {t('editAgent.agentStatus')}
                  </Typography>
                  <div className="flex items-center space-x-4">
                    <label className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="radio"
                        name="status"
                        checked={formData.active}
                        onChange={() => handleInputChange('active', true)}
                        disabled={isLoading}
                        className="text-primary focus:ring-primary"
                      />
                      <Typography variant="body2" className="text-foreground">
                        {t('editAgent.active')}
                      </Typography>
                    </label>
                    <label className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="radio"
                        name="status"
                        checked={!formData.active}
                        onChange={() => handleInputChange('active', false)}
                        disabled={isLoading}
                        className="text-primary focus:ring-primary"
                      />
                      <Typography variant="body2" className="text-foreground">
                        {t('editAgent.inactive')}
                      </Typography>
                    </label>
                  </div>
                </div>
              </Card>
            </Grid>
          </Grid>

          {/* Action Buttons */}
          <div className="flex justify-between pt-4 border-t">
            <Button
              variant="destructive"
              onClick={() => setShowDeleteModal(true)}
              disabled={isLoading}
              className="flex items-center space-x-2"
            >
              <Icon name="trash" size="sm" />
              <span>{t('editAgent.deleteAgent')}</span>
            </Button>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
              >
                {t('editAgent.cancelEdit')}
              </Button>
              <Button
                variant="default"
                onClick={handleSave}
                disabled={isLoading}
                className="flex items-center space-x-2"
              >
                <Icon name="save" size="sm" />
                <span>{t('editAgent.saveChanges')}</span>
              </Button>
            </div>
          </div>
            </div>
          </SlideInForm>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title={t('editAgent.deleteAgent')}
        size="sm"
      >
        <div className="space-y-4">
          <div className="space-y-2">
            <Typography variant="body1" className="text-foreground">
              {t('editAgent.confirmDelete')}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {t('editAgent.deleteWarning')}
            </Typography>
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowDeleteModal(false)}
              disabled={isLoading}
            >
              {t('common.cancel')}
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isLoading}
            >
              {t('editAgent.deleteAgent')}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};
