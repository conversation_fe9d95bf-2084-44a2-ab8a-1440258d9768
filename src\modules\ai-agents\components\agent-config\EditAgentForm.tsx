import React from 'react';
import { AgentListItemDto, AgentDetailDto, TypeAgentConfig } from '../../types';
import { AgentConfigurationForm } from './AgentConfigurationForm';

interface EditAgentFormProps {
  agent: AgentListItemDto;
  agentDetailData?: AgentDetailDto;
  typeAgentConfig?: TypeAgentConfig;
  onClose: () => void;
  onSuccess?: () => void;
}

export const EditAgentForm: React.FC<EditAgentFormProps> = ({
  agent,
  agentDetailData,
  typeAgentConfig,
  onClose,
  onSuccess
}) => {
  return (
    <AgentConfigurationForm
      mode="edit"
      agentId={agent.id}
      agentDetailData={agentDetailData}
      typeAgentConfig={typeAgentConfig}
      onSuccess={onSuccess}
      onCancel={onClose}
    />
  );
};
