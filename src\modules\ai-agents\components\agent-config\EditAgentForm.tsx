import { Button, Icon } from '@/shared/components/common';
import { useCorsAwareFileUpload } from '@/shared/hooks/common/useCorsAwareFileUpload';
import { NotificationUtil } from '@/shared/utils/notification';
import { t } from 'i18next';
import React, { useCallback, useEffect, useRef, useState, useMemo } from 'react';
// import { useNavigate } from 'react-router-dom'; // Không sử dụng
import { useUpdateAgentWithService, useDeleteAgentWithService, useGetTypeAgentDetailWithService } from '../../hooks/useAgentService';
// import { useGetAllBaseModels } from '../../hooks/useBaseModel'; // Không sử dụng
import {
    AgentConfigData,
    AgentDetailDto,
    ConvertData,
    IntegrationsData,
    ModelConfigData,
    MultiAgentConfigData,
    ProfileData,
    ResponseData,
    StrategyData,
    TypeAgentConfig,
    AgentListItemDto
} from '../../types';
import { mapAgentDetailDtoToAgentConfigData, mapAgentConfigDataToUpdateAgentDto } from '../../utils/agent-data-mappers';
import { TypeAgent } from '../agent-add/TypeAgentCard';
import { AgentConfigHeader } from './AgentConfigHeader';
import { AgentConfigLayout } from './AgentConfigLayout';
import {
    ConvertConfig,
    IntegrationConfig,
    MultiAgentConfig,
    ProfileConfig,
    ResponseConfig,
    StrategyConfig
} from './index';

// Import logo từ assets (không sử dụng)
// import logoImage from '@/shared/assets/images/logo/logo.png';

/**
 * Extended AgentConfigData với các flags hiển thị component
 */
export interface ExtendedAgentConfigData extends AgentConfigData {
    hasProfile?: boolean;
    hasModel?: boolean;
    hasIntegrations?: boolean;
    hasStrategy?: boolean;
    hasConvert?: boolean;
    hasResponse?: boolean;
    hasMultiAgent?: boolean;
}

interface EditAgentFormProps {
    agent: AgentListItemDto;
    agentDetailData: AgentDetailDto;
    typeAgentConfig: TypeAgentConfig;
    onClose: () => void;
    onSuccess?: () => void;
    availableAgents?: TypeAgent[];
}

export const EditAgentForm: React.FC<EditAgentFormProps> = ({
    agent,
    agentDetailData,
    typeAgentConfig,
    onClose,
    onSuccess,
    availableAgents = []
}) => {
    // const navigate = useNavigate(); // Không sử dụng
    const fileInputRef = useRef<HTMLInputElement>(null);

    // State để lưu dữ liệu form hiện tại
    const [agentData, setAgentData] = useState<ExtendedAgentConfigData | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    // State để lưu file avatar thực tế mà user upload
    const [avatarFile, setAvatarFile] = useState<File | null>(null);

    // API hooks
    const updateAgentMutation = useUpdateAgentWithService();
    const deleteAgentMutation = useDeleteAgentWithService();

    // Hook để lấy danh sách models (không sử dụng)
    // const { data: baseModelsResponse, isLoading: isLoadingModels } = useGetAllBaseModels();

    // Hook để lấy thông tin TypeAgent detail
    const { data: typeAgentDetailResponse } = useGetTypeAgentDetailWithService(agent.typeId);

    // Hook để upload file với TaskQueue (không sử dụng)
    // const fileUploadWithQueue = useCorsAwareFileUpload({
    //     defaultTaskTitle: 'Upload avatar',
    //     autoAddToQueue: true,
    // });

    // Khởi tạo dữ liệu form từ agentDetailData
    useEffect(() => {
        if (agentDetailData) {
            // Edit mode: map từ AgentDetailDto sang AgentConfigData
            const mappedData = mapAgentDetailDtoToAgentConfigData(agentDetailData);
            setAgentData({
                ...mappedData,
                // Thêm flags hiển thị component từ typeAgentConfig
                hasProfile: typeAgentConfig?.hasProfile ?? true,
                hasModel: true,
                hasIntegrations: typeAgentConfig?.hasResources ?? true,
                hasStrategy: typeAgentConfig?.hasStrategy ?? true,
                hasConvert: typeAgentConfig?.hasConversion ?? true,
                hasResponse: typeAgentConfig?.hasOutput ?? true,
                hasMultiAgent: typeAgentConfig?.hasMultiAgent ?? false,
            });
        }
    }, [agentDetailData, typeAgentConfig]);

    // Helper function để cập nhật agentData
    const updateAgentData = (updates: Partial<ExtendedAgentConfigData>) => {
        setAgentData(prev => prev ? { ...prev, ...updates } : null);
    };

    // Handler cho việc upload avatar
    const handleAvatarUpload = useCallback(() => {
        fileInputRef.current?.click();
    }, []);

    const handleAvatarFileChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            setAvatarFile(file);
            // Tạo preview URL
            const previewUrl = URL.createObjectURL(file);
            updateAgentData({
                avatar: previewUrl
            });
        }
    }, []);

    // Handler cho việc cập nhật tên agent
    const handleNameUpdate = useCallback((name: string) => {
        updateAgentData({ name });
    }, []);

    // Handler cho việc cập nhật model config
    const handleModelConfigUpdate = useCallback((modelConfig: ModelConfigData) => {
        updateAgentData({ modelConfig });
    }, []);

    // Handler cho việc cập nhật profile
    const handleProfileUpdate = useCallback((profile: ProfileData) => {
        updateAgentData({ profile });
    }, []);

    // Handler cho việc cập nhật integrations
    const handleIntegrationsUpdate = useCallback((integrations: IntegrationsData) => {
        updateAgentData({ integrations });
    }, []);

    // Handler cho việc cập nhật strategy
    const handleStrategyUpdate = useCallback((strategy: StrategyData) => {
        updateAgentData({ strategy });
    }, []);

    // Handler cho việc cập nhật response
    const handleResponseUpdate = useCallback((response: ResponseData) => {
        updateAgentData({ response });
    }, []);

    // Handler cho việc cập nhật convert
    const handleConvertUpdate = useCallback((convert: ConvertData) => {
        updateAgentData({ convert });
    }, []);

    // Handler cho việc cập nhật multi-agent
    const handleMultiAgentUpdate = useCallback((multiAgent: MultiAgentConfigData) => {
        updateAgentData({ multiAgent });
    }, []);

    // Validation cho tên agent
    const validateAgentName = useCallback((name: string): string | null => {
        if (!name.trim()) {
            return t('aiAgents.validation.nameRequired', 'Tên agent là bắt buộc');
        }
        if (name.trim().length < 2) {
            return t('aiAgents.validation.nameMinLength', 'Tên agent phải có ít nhất 2 ký tự');
        }
        if (name.trim().length > 100) {
            return t('aiAgents.validation.nameMaxLength', 'Tên agent không được vượt quá 100 ký tự');
        }
        return null;
    }, []);

    // Handler cho việc lưu agent
    const handleSave = useCallback(async () => {
        if (!agentData) return;

        // Validate tên agent
        const nameError = validateAgentName(agentData.name);
        if (nameError) {
            NotificationUtil.error({
                message: nameError,
                duration: 5000,
            });
            return;
        }

        setIsLoading(true);

        try {
            // Upload avatar nếu có file mới
            const avatarUrl = agentData.avatar;
            if (avatarFile) {
                try {
                    // TODO: Implement avatar upload với presigned URL
                    // const uploadResult = await fileUploadWithQueue.uploadToUrl(...);
                    // avatarUrl = uploadResult;
                    console.log('Avatar upload not implemented yet');
                } catch (uploadError) {
                    console.error('Avatar upload failed:', uploadError);
                    NotificationUtil.error({
                        message: t('aiAgents.avatarUploadError', 'Không thể upload avatar. Vui lòng thử lại.'),
                        duration: 5000,
                    });
                    setIsLoading(false);
                    return;
                }
            }

            // Chuẩn bị dữ liệu để update
            const updateData = mapAgentConfigDataToUpdateAgentDto({
                ...agentData,
                avatar: avatarUrl
            });

            // Gọi API update agent
            await updateAgentMutation.mutateAsync({
                id: agent.id,
                data: updateData
            });

            NotificationUtil.success({
                message: t('aiAgents.updateSuccess', 'Cập nhật agent thành công!'),
                duration: 3000,
            });

            // Gọi callback success và đóng form
            onSuccess?.();
            onClose();

        } catch (error) {
            console.error('Update agent error:', error);
            NotificationUtil.error({
                message: t('aiAgents.updateError', 'Cập nhật agent thất bại. Vui lòng thử lại.'),
                duration: 5000,
            });
        } finally {
            setIsLoading(false);
        }
    }, [agentData, agent.id, avatarFile, updateAgentMutation, validateAgentName, onSuccess, onClose]);

    // Handler cho việc hủy
    const handleCancel = useCallback(() => {
        onClose();
    }, [onClose]);

    // Handler cho việc xóa agent
    const handleDelete = useCallback(async () => {
        if (!window.confirm(t('aiAgents.confirmDelete', 'Bạn có chắc chắn muốn xóa agent này không?'))) {
            return;
        }

        setIsLoading(true);

        try {
            await deleteAgentMutation.mutateAsync(agent.id);

            NotificationUtil.success({
                message: t('aiAgents.deleteSuccess', 'Xóa agent thành công!'),
                duration: 3000,
            });

            // Gọi callback success và đóng form
            onSuccess?.();
            onClose();

        } catch (error) {
            console.error('Delete agent error:', error);
            NotificationUtil.error({
                message: t('aiAgents.deleteError', 'Xóa agent thất bại. Vui lòng thử lại.'),
                duration: 5000,
            });
        } finally {
            setIsLoading(false);
        }
    }, [agent.id, deleteAgentMutation, onSuccess, onClose]);

    // Kiểm tra xem có thay đổi nào không
    const hasChanges = useMemo(() => {
        if (!agentData || !agentDetailData) return false;

        // So sánh dữ liệu hiện tại với dữ liệu ban đầu
        const originalData = mapAgentDetailDtoToAgentConfigData(agentDetailData);

        return (
            agentData.name !== originalData.name ||
            JSON.stringify(agentData.modelConfig) !== JSON.stringify(originalData.modelConfig) ||
            JSON.stringify(agentData.profile) !== JSON.stringify(originalData.profile) ||
            JSON.stringify(agentData.integrations) !== JSON.stringify(originalData.integrations) ||
            JSON.stringify(agentData.strategy) !== JSON.stringify(originalData.strategy) ||
            JSON.stringify(agentData.response) !== JSON.stringify(originalData.response) ||
            JSON.stringify(agentData.convert) !== JSON.stringify(originalData.convert) ||
            JSON.stringify(agentData.multiAgent) !== JSON.stringify(originalData.multiAgent) ||
            avatarFile !== null
        );
    }, [agentData, agentDetailData, avatarFile]);

    // Kiểm tra trạng thái saving
    const isSaving = isLoading || updateAgentMutation.isPending || deleteAgentMutation.isPending;

    // Hiển thị loading nếu chưa có dữ liệu
    if (!agentData) {
        return (
            <div className="flex justify-center items-center py-12">
                <div className="text-gray-500">Đang tải dữ liệu...</div>
            </div>
        );
    }

    return (
        <div className="bg-white dark:bg-gray-900 min-h-screen">
            {/* Header với nút đóng */}
            <div className="flex justify-between items-center p-6 border-b">
                <div>
                    <h1 className="text-2xl font-bold text-foreground">
                        {t('aiAgents.editAgent.title', 'Chỉnh sửa Agent')}
                    </h1>
                    <p className="text-muted-foreground mt-1">
                        {t('aiAgents.editAgent.subtitle', 'Cập nhật thông tin và cấu hình của Agent')}
                    </p>
                </div>
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClose}
                    className="flex items-center space-x-2"
                >
                    <Icon name="x" size="sm" />
                    <span>{t('common.close', 'Đóng')}</span>
                </Button>
            </div>

            {/* Layout mới với Left Panel + Right Content */}
            <AgentConfigLayout
                typeAgentConfig={typeAgentConfig || {}}
            >
                {{
                    leftPanel: (
                        <AgentConfigHeader
                            agentData={agentData}
                            typeAgentName={typeAgentDetailResponse?.result?.name}
                            onAvatarUpload={handleAvatarUpload}
                            onAvatarFileChange={handleAvatarFileChange}
                            onNameUpdate={handleNameUpdate}
                            onModelConfigUpdate={handleModelConfigUpdate}
                            validateAgentName={validateAgentName}
                            fileInputRef={fileInputRef}
                            onSave={handleSave}
                            onCancel={handleCancel}
                            onDelete={handleDelete}
                            isSaving={isSaving}
                            hasChanges={hasChanges}
                            mode="edit"
                        />
                    ),

                    profileConfig: typeAgentConfig?.hasProfile ? (
                        <ProfileConfig
                            initialData={agentData.profile}
                            onSave={handleProfileUpdate}
                        />
                    ) : undefined,

                    integrationConfig: typeAgentConfig?.hasResources ? (
                        <IntegrationConfig
                            initialData={agentData.integrations}
                            onSave={handleIntegrationsUpdate}
                        />
                    ) : undefined,

                    strategyConfig: typeAgentConfig?.hasStrategy ? (
                        <StrategyConfig
                            initialData={agentData.strategy}
                            onSave={handleStrategyUpdate}
                        />
                    ) : undefined,

                    responseConfig: typeAgentConfig?.hasOutput ? (
                        <ResponseConfig
                            initialData={agentData.response}
                            onSave={handleResponseUpdate}
                        />
                    ) : undefined,

                    convertConfig: typeAgentConfig?.hasConversion ? (
                        <ConvertConfig
                            initialData={agentData.convert}
                            onSave={handleConvertUpdate}
                        />
                    ) : undefined,

                    multiAgentConfig: typeAgentConfig?.hasMultiAgent ? (
                        <MultiAgentConfig
                            initialData={agentData.multiAgent}
                            onSave={handleMultiAgentUpdate}
                            availableAgents={availableAgents}
                        />
                    ) : undefined,
                }}
            </AgentConfigLayout>
        </div>
    );
};
